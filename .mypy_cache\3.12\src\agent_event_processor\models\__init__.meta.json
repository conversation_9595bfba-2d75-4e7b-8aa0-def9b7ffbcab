{"data_mtime": 1757704608, "dep_lines": [3, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["src.agent_event_processor.models.events", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "8369dd0c80f962a926632acb890f9281285b5d99", "id": "src.agent_event_processor.models", "ignore_all": false, "interface_hash": "796e53748205e96acd403b13045aca49dc990aff", "mtime": 1757701537, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\agent-event-processor\\src\\agent_event_processor\\models\\__init__.py", "plugin_data": null, "size": 164, "suppressed": [], "version_id": "1.17.1"}