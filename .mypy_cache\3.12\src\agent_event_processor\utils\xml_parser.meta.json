{"data_mtime": 1757704607, "dep_lines": [9, 9, 7, 8, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["xml.parsers.expat", "xml.parsers", "json", "typing", "xmltodict", "aws_lambda_powertools", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "aws_lambda_powertools.logging", "aws_lambda_powertools.logging.buffer", "aws_lambda_powertools.logging.buffer.config", "aws_lambda_powertools.logging.logger", "collections", "json.decoder", "logging", "types"], "hash": "0bfb7c8853957daacb7b2597e090b9d9e86f5062", "id": "src.agent_event_processor.utils.xml_parser", "ignore_all": false, "interface_hash": "39d8c193363d04e880afddf5e83bf7855b998cd2", "mtime": 1757700702, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\agent-event-processor\\src\\agent_event_processor\\utils\\xml_parser.py", "plugin_data": null, "size": 4169, "suppressed": [], "version_id": "1.17.1"}