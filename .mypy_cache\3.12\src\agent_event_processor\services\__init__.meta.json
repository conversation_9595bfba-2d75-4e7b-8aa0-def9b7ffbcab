{"data_mtime": 1758118220, "dep_lines": [3, 4, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30], "dependencies": ["src.agent_event_processor.services.database_repository", "src.agent_event_processor.services.database_service", "src.agent_event_processor.services.event_processor", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "610a36bce01851615ec6d9ca13df9491c23dd003", "id": "src.agent_event_processor.services", "ignore_all": false, "interface_hash": "c28c881a2e70695f12f05ddbf2f3df165691f026", "mtime": 1757701832, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\agent-event-processor\\src\\agent_event_processor\\services\\__init__.py", "plugin_data": null, "size": 393, "suppressed": [], "version_id": "1.17.1"}