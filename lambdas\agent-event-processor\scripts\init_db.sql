-- Replace {{SCHEM<PERSON>}} / {{OWNER}} as needed
CREATE SCHEMA IF NOT EXISTS {{SCHEMA}};

-- Tenants: tiny dim → replicate to all nodes
CREATE TABLE IF NOT EXISTS {{SCHEMA}}.dim_tenant (
    tenant_key     INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_name    VARCHAR(256) NOT NULL UNIQUE,
    timezone_name  VARCHAR(256) NOT NULL,
    created_at_utc TIMESTAMP DEFAULT GETDATE() NOT NULL
)
DISTSTYLE ALL
SORTKEY AUTO;
ALTER TABLE {{SCHEMA}}.dim_tenant OWNER TO {{OWNER}};

-- Agents: tiny dim → replicate; sort to speed lookups
CREATE TABLE IF NOT EXISTS {{SCHEMA}}.dim_agent (
    agent_key      INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_key     INTEGER NOT NULL,
    agent_name     VARCHA<PERSON>(256) NOT NULL,
    agent_role     VARCHAR(256),
    created_at_utc TIMESTAMP DEFAULT GETDATE() NOT NULL,
    UNIQUE (tenant_key, agent_name, agent_role)
)
DISTSTYLE ALL
SORTKEY (tenant_key, agent_name);
ALTER TABLE {{SCHEMA}}.dim_agent OWNER TO {{OWNER}};

-- Ring groups: tiny dim → replicate; sort for tenant/name probes
CREATE TABLE IF NOT EXISTS {{SCHEMA}}.dim_ring_group (
    ring_group_key INTEGER IDENTITY(1,1) PRIMARY KEY,
    tenant_key     INTEGER NOT NULL,
    ring_group_name VARCHAR(256) NOT NULL,
    ring_group_uri  VARCHAR(256) NOT NULL,
    created_at_utc  TIMESTAMP DEFAULT GETDATE(),
    UNIQUE (tenant_key, ring_group_name, ring_group_uri)
)
DISTSTYLE ALL
SORTKEY (tenant_key, ring_group_name);
ALTER TABLE {{SCHEMA}}.dim_ring_group OWNER TO {{OWNER}};

-- Facts: colocate by tenant, prune by (tenant, time)
CREATE TABLE IF NOT EXISTS {{SCHEMA}}.dim_agent_event (
    event_key           BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key           INTEGER NOT NULL,
    ring_group_key      INTEGER,
    tenant_key          INTEGER NOT NULL,
    event_type          VARCHAR(256) NOT NULL ENCODE ZSTD,
    hash_event          VARCHAR(64)  NOT NULL ENCODE ZSTD,
    timestamp_utc       TIMESTAMP    NOT NULL,
    timestamp_local     TIMESTAMP    NOT NULL,
    operator_id         VARCHAR(256) ENCODE ZSTD,
    workstation         VARCHAR(256) ENCODE ZSTD,
    media_label         VARCHAR(256) ENCODE ZSTD,
    uri                 VARCHAR(256) ENCODE ZSTD,
    device_name         VARCHAR(256) ENCODE ZSTD,
    busied_out_action   VARCHAR(256) ENCODE ZSTD,
    busied_out_duration INTEGER      ENCODE ZSTD,
    reason_code         VARCHAR(256) ENCODE ZSTD,
    json_data           SUPER        NOT NULL,
    created_at_utc      TIMESTAMP DEFAULT GETDATE() NOT NULL,
    UNIQUE (hash_event)
)
DISTKEY (tenant_key)
SORTKEY  (tenant_key, timestamp_utc);
ALTER TABLE {{SCHEMA}}.dim_agent_event OWNER TO {{OWNER}};