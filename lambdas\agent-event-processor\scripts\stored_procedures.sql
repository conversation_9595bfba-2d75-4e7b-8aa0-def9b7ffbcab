CREATE OR REPLACE PROCEDURE {{SCHEMA}}.sp_ingest_agent_event(
    p_tenant_name          VARCHAR(256),
    p_timezone_name        VARCHA<PERSON>(256),
    p_agent_name           VARCHAR(256),
    p_agent_role           VARCHAR(256),
    p_event_type           VARCHAR(256),
    p_hash_event           VARCHAR(64),
    p_timestamp_utc        TIMESTAMP,
    p_timestamp_local      TIMESTAMP,
    p_operator_id          VARCHAR(256),
    p_workstation          VARCHAR(256),
    p_media_label          VARCHAR(256),
    p_uri                  VARCHAR(256),
    p_device_name          VARCHAR(256),
    p_busied_out_action    VARCHAR(256),
    p_busied_out_duration  INTEGER,
    p_reason_code          VARCHAR(256),
    p_ring_group_name      VARCHAR(256),
    p_ring_group_uri       VARCHAR(256),
    p_json                 VARCHAR(65535)
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_tenant_key      INTEGER;
    v_agent_key       INTEGER;
    v_ring_group_key  INTEGER;
    v_error_context   VARCHAR(256);
BEGIN
    v_error_context := 'Validating inputs';
    IF p_tenant_name IS NULL OR p_tenant_name = '' THEN
        RAISE EXCEPTION 'tenant_name is required';
    END IF;
    IF p_agent_name IS NULL OR p_agent_name = '' THEN
        RAISE EXCEPTION 'agent_name is required';
    END IF;
    IF p_event_type IS NULL OR p_event_type = '' THEN
        RAISE EXCEPTION 'event_type is required';
    END IF;
    IF p_hash_event IS NULL OR LENGTH(p_hash_event) <> 64 THEN
        RAISE EXCEPTION 'hash_event must be 64-character SHA-256';
    END IF;
    IF p_timestamp_utc IS NULL OR p_timestamp_local IS NULL THEN
        RAISE EXCEPTION 'Both timestamp_utc and timestamp_local are required';
    END IF;


    -- TENANT upsert using MERGE
    v_error_context := 'Upserting tenant';

    MERGE INTO {{SCHEMA}}.dim_tenant
    USING (
        SELECT p_tenant_name AS tenant_name, p_timezone_name AS timezone_name
    ) source_data
    ON {{SCHEMA}}.dim_tenant.tenant_name = source_data.tenant_name
    WHEN MATCHED THEN
        UPDATE SET timezone_name = source_data.timezone_name
    WHEN NOT MATCHED THEN
        INSERT (tenant_name, timezone_name)
        VALUES (source_data.tenant_name, source_data.timezone_name);

    -- Get tenant key
    SELECT tenant_key INTO v_tenant_key
    FROM {{SCHEMA}}.dim_tenant
    WHERE tenant_name = p_tenant_name;

    IF v_tenant_key IS NULL THEN
        RAISE EXCEPTION 'Could not resolve tenant_key for %', p_tenant_name;
    END IF;

    -- AGENT upsert using MERGE
    v_error_context := 'Upserting agent';

    MERGE INTO {{SCHEMA}}.dim_agent
    USING (
        SELECT v_tenant_key AS tenant_key,
               p_agent_name AS agent_name,
               p_agent_role AS agent_role
    ) source_data
    ON {{SCHEMA}}.dim_agent.tenant_key = source_data.tenant_key
       AND {{SCHEMA}}.dim_agent.agent_name = source_data.agent_name
       AND {{SCHEMA}}.dim_agent.agent_role = source_data.agent_role
    WHEN MATCHED THEN
        -- Need to update at least one column to avoid syntax error
        UPDATE SET agent_name = source_data.agent_name
    WHEN NOT MATCHED THEN
        INSERT (tenant_key, agent_name, agent_role)
        VALUES (source_data.tenant_key, source_data.agent_name, source_data.agent_role);

    -- Get agent key
    SELECT agent_key INTO v_agent_key
    FROM {{SCHEMA}}.dim_agent
    WHERE tenant_key = v_tenant_key
      AND agent_name = p_agent_name
      AND agent_role = p_agent_role;

    IF v_agent_key IS NULL THEN
        RAISE EXCEPTION 'Could not resolve agent_key for %/%', p_agent_name, p_agent_role;
    END IF;

    -- Optional RING GROUP upsert using MERGE
    v_ring_group_key := NULL;
    IF p_ring_group_name IS NOT NULL AND p_ring_group_uri IS NOT NULL THEN
        v_error_context := 'Upserting ring group';

        MERGE INTO {{SCHEMA}}.dim_ring_group
        USING (
            SELECT v_tenant_key AS tenant_key,
                   p_ring_group_name AS ring_group_name,
                   p_ring_group_uri AS ring_group_uri
        ) source_data
        ON {{SCHEMA}}.dim_ring_group.tenant_key = source_data.tenant_key
           AND {{SCHEMA}}.dim_ring_group.ring_group_name = source_data.ring_group_name
           AND {{SCHEMA}}.dim_ring_group.ring_group_uri = source_data.ring_group_uri
        WHEN MATCHED THEN
            -- Need to update at least one column to avoid syntax error
            UPDATE SET ring_group_name = source_data.ring_group_name
        WHEN NOT MATCHED THEN
            INSERT (tenant_key, ring_group_name, ring_group_uri)
            VALUES (source_data.tenant_key, source_data.ring_group_name, source_data.ring_group_uri);

        -- Get ring group key
        SELECT ring_group_key INTO v_ring_group_key
        FROM {{SCHEMA}}.dim_ring_group
        WHERE tenant_key = v_tenant_key
          AND ring_group_name = p_ring_group_name
          AND ring_group_uri = p_ring_group_uri;
    END IF;

    -- EVENT idempotent insert using MERGE
    v_error_context := 'Inserting event';

    MERGE INTO {{SCHEMA}}.dim_agent_event
    USING (
        SELECT v_agent_key AS agent_key,
               v_ring_group_key AS ring_group_key,
               v_tenant_key AS tenant_key,
               p_event_type AS event_type,
               p_hash_event AS hash_event,
               p_timestamp_utc AS timestamp_utc,
               p_timestamp_local AS timestamp_local,
               p_operator_id AS operator_id,
               p_workstation AS workstation,
               p_media_label AS media_label,
               p_uri AS uri,
               p_device_name AS device_name,
               p_busied_out_action AS busied_out_action,
               p_busied_out_duration AS busied_out_duration,
               p_reason_code AS reason_code,
               CASE
                   WHEN p_json IS NULL OR p_json = '' THEN JSON_PARSE('{}')
                   ELSE JSON_PARSE(p_json)
               END AS json_data
    ) source_data
    ON {{SCHEMA}}.dim_agent_event.hash_event = source_data.hash_event
    WHEN MATCHED THEN
        UPDATE SET event_type = source_data.event_type  -- No-op update for idempotency
    WHEN NOT MATCHED THEN
        INSERT (agent_key, ring_group_key, tenant_key,
                event_type, hash_event,
                timestamp_utc, timestamp_local,
                operator_id, workstation, media_label, uri, device_name,
                busied_out_action, busied_out_duration, reason_code, json_data)
        VALUES (source_data.agent_key, source_data.ring_group_key, source_data.tenant_key,
                source_data.event_type, source_data.hash_event,
                source_data.timestamp_utc, source_data.timestamp_local,
                source_data.operator_id, source_data.workstation, source_data.media_label,
                source_data.uri, source_data.device_name,
                source_data.busied_out_action, source_data.busied_out_duration,
                source_data.reason_code, source_data.json_data);

    RAISE NOTICE 'Event processing completed (hash: %)', p_hash_event;
    RETURN;

EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'Error in {{SCHEMA}}.sp_ingest_agent_event (%): %', v_error_context, SQLERRM;
END;
$$;
