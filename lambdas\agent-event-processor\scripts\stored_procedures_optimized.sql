-- Optimized stored procedure for better concurrent performance
-- Key improvements:
-- 1. Conditional updates in MERGE to avoid unnecessary writes
-- 2. INSERT-first pattern for dimension tables to reduce lock contention
-- 3. Better error handling for concurrent operations
-- 4. Reduced transaction scope

CREATE OR REPLACE PROCEDURE {{SCHEMA}}.sp_ingest_agent_event_optimized(
    p_tenant_name          VARCHAR(256),
    p_timezone_name        VARCHAR(256),
    p_agent_name           VARCHAR(256),
    p_agent_role           VARCHAR(256),
    p_event_type           VARCHAR(256),
    p_hash_event           VARCHAR(64),
    p_timestamp_utc        TIMESTAMP,
    p_timestamp_local      TIMESTAMP,
    p_operator_id          VARCHAR(256),
    p_workstation          VARCHAR(256),
    p_media_label          VARCHAR(256),
    p_uri                  VARCHAR(256),
    p_device_name          VARCHAR(256),
    p_busied_out_action    VARCHAR(256),
    p_busied_out_duration  INTEGER,
    p_reason_code          VARCHAR(256),
    p_ring_group_name      VARCHA<PERSON>(256),
    p_ring_group_uri       <PERSON>(256),
    p_j<PERSON>                 VARCHAR(65535)
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_tenant_key      INTEGER;
    v_agent_key       INTEGER;
    v_ring_group_key  INTEGER;
    v_error_context   VARCHAR(256);
    v_existing_timezone VARCHAR(256);
    v_existing_role   VARCHAR(256);
    v_existing_rg_name VARCHAR(256);
    v_existing_rg_uri VARCHAR(256);
BEGIN
    v_error_context := 'Validating inputs';
    IF p_tenant_name IS NULL OR p_tenant_name = '' THEN
        RAISE EXCEPTION 'tenant_name is required';
    END IF;
    IF p_agent_name IS NULL OR p_agent_name = '' THEN
        RAISE EXCEPTION 'agent_name is required';
    END IF;
    IF p_event_type IS NULL OR p_event_type = '' THEN
        RAISE EXCEPTION 'event_type is required';
    END IF;
    IF p_hash_event IS NULL OR LENGTH(p_hash_event) <> 64 THEN
        RAISE EXCEPTION 'hash_event must be 64-character SHA-256';
    END IF;
    IF p_timestamp_utc IS NULL OR p_timestamp_local IS NULL THEN
        RAISE EXCEPTION 'Both timestamp_utc and timestamp_local are required';
    END IF;

    -- TENANT optimized upsert
    v_error_context := 'Upserting tenant';
    
    -- First try to get existing tenant
    SELECT tenant_key, timezone_name INTO v_tenant_key, v_existing_timezone
    FROM {{SCHEMA}}.dim_tenant
    WHERE tenant_name = p_tenant_name;
    
    IF v_tenant_key IS NULL THEN
        -- Tenant doesn't exist, try INSERT first (faster for new records)
        BEGIN
            INSERT INTO {{SCHEMA}}.dim_tenant (tenant_name, timezone_name)
            VALUES (p_tenant_name, p_timezone_name);
            
            -- Get the newly inserted key
            SELECT tenant_key INTO v_tenant_key
            FROM {{SCHEMA}}.dim_tenant
            WHERE tenant_name = p_tenant_name;
            
        EXCEPTION WHEN unique_violation THEN
            -- Another process inserted it, just get the key
            SELECT tenant_key, timezone_name INTO v_tenant_key, v_existing_timezone
            FROM {{SCHEMA}}.dim_tenant
            WHERE tenant_name = p_tenant_name;
        END;
    END IF;
    
    -- Only update if timezone actually changed
    IF v_existing_timezone IS NOT NULL AND v_existing_timezone != p_timezone_name THEN
        UPDATE {{SCHEMA}}.dim_tenant 
        SET timezone_name = p_timezone_name
        WHERE tenant_key = v_tenant_key;
    END IF;

    IF v_tenant_key IS NULL THEN
        RAISE EXCEPTION 'Could not resolve tenant_key for %', p_tenant_name;
    END IF;

    -- AGENT optimized upsert
    v_error_context := 'Upserting agent';
    
    -- First try to get existing agent
    SELECT agent_key, agent_role INTO v_agent_key, v_existing_role
    FROM {{SCHEMA}}.dim_agent
    WHERE tenant_key = v_tenant_key
      AND agent_name = p_agent_name
      AND (agent_role = p_agent_role OR (agent_role IS NULL AND p_agent_role IS NULL));
    
    IF v_agent_key IS NULL THEN
        -- Agent doesn't exist, try INSERT first
        BEGIN
            INSERT INTO {{SCHEMA}}.dim_agent (tenant_key, agent_name, agent_role)
            VALUES (v_tenant_key, p_agent_name, p_agent_role);
            
            -- Get the newly inserted key
            SELECT agent_key INTO v_agent_key
            FROM {{SCHEMA}}.dim_agent
            WHERE tenant_key = v_tenant_key
              AND agent_name = p_agent_name
              AND (agent_role = p_agent_role OR (agent_role IS NULL AND p_agent_role IS NULL));
              
        EXCEPTION WHEN unique_violation THEN
            -- Another process inserted it, just get the key
            SELECT agent_key, agent_role INTO v_agent_key, v_existing_role
            FROM {{SCHEMA}}.dim_agent
            WHERE tenant_key = v_tenant_key
              AND agent_name = p_agent_name
              AND (agent_role = p_agent_role OR (agent_role IS NULL AND p_agent_role IS NULL));
        END;
    END IF;
    
    -- Only update if role actually changed (though this is rare for agents)
    IF v_existing_role IS NOT NULL AND 
       ((v_existing_role != p_agent_role) OR 
        (v_existing_role IS NULL AND p_agent_role IS NOT NULL) OR
        (v_existing_role IS NOT NULL AND p_agent_role IS NULL)) THEN
        UPDATE {{SCHEMA}}.dim_agent 
        SET agent_role = p_agent_role
        WHERE agent_key = v_agent_key;
    END IF;

    IF v_agent_key IS NULL THEN
        RAISE EXCEPTION 'Could not resolve agent_key for %/%', p_agent_name, p_agent_role;
    END IF;

    -- Optional RING GROUP optimized upsert
    v_ring_group_key := NULL;
    IF p_ring_group_name IS NOT NULL AND p_ring_group_uri IS NOT NULL THEN
        v_error_context := 'Upserting ring group';
        
        -- First try to get existing ring group
        SELECT ring_group_key, ring_group_name, ring_group_uri 
        INTO v_ring_group_key, v_existing_rg_name, v_existing_rg_uri
        FROM {{SCHEMA}}.dim_ring_group
        WHERE tenant_key = v_tenant_key
          AND ring_group_name = p_ring_group_name
          AND ring_group_uri = p_ring_group_uri;
        
        IF v_ring_group_key IS NULL THEN
            -- Ring group doesn't exist, try INSERT first
            BEGIN
                INSERT INTO {{SCHEMA}}.dim_ring_group (tenant_key, ring_group_name, ring_group_uri)
                VALUES (v_tenant_key, p_ring_group_name, p_ring_group_uri);
                
                -- Get the newly inserted key
                SELECT ring_group_key INTO v_ring_group_key
                FROM {{SCHEMA}}.dim_ring_group
                WHERE tenant_key = v_tenant_key
                  AND ring_group_name = p_ring_group_name
                  AND ring_group_uri = p_ring_group_uri;
                  
            EXCEPTION WHEN unique_violation THEN
                -- Another process inserted it, just get the key
                SELECT ring_group_key INTO v_ring_group_key
                FROM {{SCHEMA}}.dim_ring_group
                WHERE tenant_key = v_tenant_key
                  AND ring_group_name = p_ring_group_name
                  AND ring_group_uri = p_ring_group_uri;
            END;
        END IF;
        
        -- Ring groups rarely change, so no conditional update needed
    END IF;

    -- EVENT idempotent insert - this is the most important optimization
    v_error_context := 'Inserting event';
    
    -- For events, we only want to insert if hash doesn't exist
    -- Use INSERT first since most events are new
    BEGIN
        INSERT INTO {{SCHEMA}}.dim_agent_event (
            agent_key, ring_group_key, tenant_key,
            event_type, hash_event,
            timestamp_utc, timestamp_local,
            operator_id, workstation, media_label, uri, device_name,
            busied_out_action, busied_out_duration, reason_code, json_data
        ) VALUES (
            v_agent_key, v_ring_group_key, v_tenant_key,
            p_event_type, p_hash_event,
            p_timestamp_utc, p_timestamp_local,
            p_operator_id, p_workstation, p_media_label, p_uri, p_device_name,
            p_busied_out_action, p_busied_out_duration, p_reason_code,
            CASE
                WHEN p_json IS NULL OR p_json = '' THEN JSON_PARSE('{}')
                ELSE JSON_PARSE(p_json)
            END
        );
        
        RAISE NOTICE 'Event inserted successfully (hash: %)', p_hash_event;
        
    EXCEPTION WHEN unique_violation THEN
        -- Event already exists (duplicate), this is expected for idempotency
        RAISE NOTICE 'Event already exists, skipping (hash: %)', p_hash_event;
    END;

    RETURN;

EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'Error in {{SCHEMA}}.sp_ingest_agent_event_optimized (%): %', v_error_context, SQLERRM;
END;
$$;
