#!/usr/bin/env python3
"""Setup test schema and deploy optimized stored procedures for performance testing.

This script creates a test_schema in Redshift and deploys both the original
and optimized stored procedures for performance comparison.

Usage:
    $env:AWS_PROFILE="admin-memo"; python setup_test_schema.py
"""

import os
import sys
from pathlib import Path

import boto3
from botocore.exceptions import ClientError


def get_redshift_client():
    """Get Redshift Data API client."""
    return boto3.client('redshift-data', region_name='us-east-1')


def execute_sql(client, cluster_id: str, database: str, user: str, sql: str, description: str = ""):
    """Execute SQL and wait for completion."""
    print(f"Executing: {description or sql[:50]}...")
    
    try:
        response = client.execute_statement(
            ClusterIdentifier=cluster_id,
            Database=database,
            DbUser=user,
            Sql=sql,
            WithEvent=True
        )
        
        statement_id = response['Id']
        print(f"Statement ID: {statement_id}")
        
        # Wait for completion
        waiter = client.get_waiter('statement_complete')
        waiter.wait(Id=statement_id, WaiterConfig={'Delay': 2, 'MaxAttempts': 30})
        
        # Check result
        result = client.describe_statement(Id=statement_id)
        status = result['Status']
        
        if status == 'FINISHED':
            print(f"✓ Success: {description}")
            return True
        else:
            print(f"✗ Failed: {description}")
            print(f"Status: {status}")
            if 'Error' in result:
                print(f"Error: {result['Error']}")
            return False
            
    except ClientError as e:
        print(f"✗ AWS Error executing {description}: {e}")
        return False
    except Exception as e:
        print(f"✗ Error executing {description}: {e}")
        return False


def load_sql_file(file_path: Path, schema_name: str = "test_schema", owner: str = "solacom") -> str:
    """Load SQL file and replace template variables."""
    with open(file_path, 'r') as f:
        sql = f.read()
    
    # Replace template variables
    sql = sql.replace('{{SCHEMA}}', schema_name)
    sql = sql.replace('{{OWNER}}', owner)
    
    return sql


def main():
    """Setup test schema and deploy stored procedures."""
    print("Setting up test schema for stored procedure performance testing")
    print("=" * 70)
    
    # Configuration
    cluster_id = "dev-us-smartanalytics-common-redshift"
    database = "dev"
    user = "solacom"
    test_schema = "test_schema"
    
    # Get Redshift client
    client = get_redshift_client()
    
    # Script directory
    script_dir = Path(__file__).parent / "scripts"
    
    print(f"Cluster: {cluster_id}")
    print(f"Database: {database}")
    print(f"User: {user}")
    print(f"Test Schema: {test_schema}")
    print()
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Create test schema
    print("Step 1: Creating test schema...")
    create_schema_sql = f"""
    DROP SCHEMA IF EXISTS {test_schema} CASCADE;
    CREATE SCHEMA {test_schema};
    """
    
    if execute_sql(client, cluster_id, database, user, create_schema_sql, "Create test schema"):
        success_count += 1
    
    # Step 2: Create tables in test schema
    print("\nStep 2: Creating tables in test schema...")
    init_db_sql = load_sql_file(script_dir / "init_db.sql", test_schema, user)
    
    if execute_sql(client, cluster_id, database, user, init_db_sql, "Create tables"):
        success_count += 1
    
    # Step 3: Deploy original stored procedure
    print("\nStep 3: Deploying original stored procedure...")
    original_proc_sql = load_sql_file(script_dir / "stored_procedures.sql", test_schema, user)
    
    if execute_sql(client, cluster_id, database, user, original_proc_sql, "Deploy original stored procedure"):
        success_count += 1
    
    # Step 4: Deploy optimized stored procedure
    print("\nStep 4: Deploying optimized stored procedure...")
    optimized_proc_sql = load_sql_file(script_dir / "stored_procedures_optimized.sql", test_schema, user)
    
    if execute_sql(client, cluster_id, database, user, optimized_proc_sql, "Deploy optimized stored procedure"):
        success_count += 1
    
    # Step 5: Verify deployment
    print("\nStep 5: Verifying deployment...")
    verify_sql = f"""
    SELECT 
        schemaname,
        proname,
        proargnames
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = '{test_schema}'
      AND p.proname LIKE 'sp_ingest_agent_event%'
    ORDER BY p.proname;
    """
    
    if execute_sql(client, cluster_id, database, user, verify_sql, "Verify stored procedures"):
        success_count += 1
    
    # Summary
    print("\n" + "=" * 70)
    print("Setup Summary:")
    print("=" * 70)
    
    if success_count == total_steps:
        print(f"✓ All {total_steps} steps completed successfully!")
        print(f"✓ Test schema '{test_schema}' is ready for performance testing")
        print()
        print("Available stored procedures:")
        print(f"  - {test_schema}.sp_ingest_agent_event (original)")
        print(f"  - {test_schema}.sp_ingest_agent_event_optimized (optimized)")
        print()
        print("Next steps:")
        print("  1. Run: python test_stored_procedure_performance.py")
        print("  2. Compare performance results")
        print("  3. Review optimization recommendations")
    else:
        print(f"✗ Only {success_count}/{total_steps} steps completed successfully")
        print("Check the error messages above and retry")
        sys.exit(1)


if __name__ == "__main__":
    main()
