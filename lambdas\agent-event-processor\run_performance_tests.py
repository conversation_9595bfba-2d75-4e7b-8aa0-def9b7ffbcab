#!/usr/bin/env python3
"""Convenience script to run the complete performance testing suite.

This script runs the setup and performance tests in sequence.

Usage:
    $env:AWS_PROFILE="admin-memo"; python run_performance_tests.py
    $env:AWS_PROFILE="admin-memo"; python run_performance_tests.py --quick
    $env:AWS_PROFILE="admin-memo"; python run_performance_tests.py --stress
"""

import argparse
import subprocess
import sys
import time
from pathlib import Path


def run_command(command: str, description: str) -> bool:
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=False)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with exit code {e.returncode}")
        return False
    except Exception as e:
        print(f"✗ {description} failed with error: {e}")
        return False


def main():
    """Main function to run the complete test suite."""
    parser = argparse.ArgumentParser(description='Run complete performance testing suite')
    parser.add_argument('--quick', action='store_true', help='Run quick test (3 concurrent, 5 iterations)')
    parser.add_argument('--stress', action='store_true', help='Run stress test (20 concurrent, 50 iterations)')
    parser.add_argument('--setup-only', action='store_true', help='Only run setup, skip performance tests')
    parser.add_argument('--test-only', action='store_true', help='Only run tests, skip setup')
    
    args = parser.parse_args()
    
    print("Stored Procedure Performance Testing Suite")
    print("=" * 70)
    print("This script will:")
    if not args.test_only:
        print("  1. Setup test schema and deploy stored procedures")
    if not args.setup_only:
        print("  2. Run performance tests comparing original vs optimized")
        print("  3. Generate performance comparison report")
    print()
    
    # Determine test parameters
    if args.quick:
        concurrent = 3
        iterations = 5
        test_type = "Quick Test"
    elif args.stress:
        concurrent = 20
        iterations = 50
        test_type = "Stress Test"
    else:
        concurrent = 5
        iterations = 10
        test_type = "Standard Test"
    
    print(f"Test Configuration: {test_type}")
    print(f"  - Concurrent threads: {concurrent}")
    print(f"  - Iterations per thread: {iterations}")
    print(f"  - Total operations per procedure: {concurrent * iterations}")
    print()
    
    success_count = 0
    total_steps = 0
    
    # Step 1: Setup test schema (unless --test-only)
    if not args.test_only:
        total_steps += 1
        if run_command("python setup_test_schema.py", "Setup test schema and stored procedures"):
            success_count += 1
            print("\n⏳ Waiting 5 seconds for schema setup to complete...")
            time.sleep(5)
        else:
            print("❌ Setup failed. Cannot proceed with performance tests.")
            sys.exit(1)
    
    # Step 2: Run performance tests (unless --setup-only)
    if not args.setup_only:
        total_steps += 1
        test_command = f"python test_stored_procedure_performance.py --concurrent {concurrent} --iterations {iterations}"
        
        if run_command(test_command, f"Performance testing ({test_type})"):
            success_count += 1
        else:
            print("❌ Performance tests failed.")
    
    # Summary
    print(f"\n{'='*70}")
    print("EXECUTION SUMMARY")
    print(f"{'='*70}")
    
    if success_count == total_steps:
        print(f"✅ All {total_steps} steps completed successfully!")
        print()
        print("📊 Performance Test Results:")
        print("   - Check the output above for detailed performance comparison")
        print("   - Look for 'PERFORMANCE COMPARISON' section")
        print("   - Note the improvement percentages and throughput metrics")
        print()
        print("🔍 Key Metrics to Review:")
        print("   - Average execution time improvement")
        print("   - Throughput (operations per second) improvement")
        print("   - Success rate comparison")
        print("   - Error reduction")
        print()
        print("📋 Next Steps:")
        print("   1. Review the performance improvements")
        print("   2. If satisfied, consider deploying optimized procedure to production")
        print("   3. Monitor production performance after deployment")
        print("   4. Clean up test schema when no longer needed:")
        print("      DROP SCHEMA test_schema CASCADE;")
        
    else:
        print(f"❌ Only {success_count}/{total_steps} steps completed successfully")
        print("Check the error messages above and retry")
        sys.exit(1)


if __name__ == "__main__":
    main()
