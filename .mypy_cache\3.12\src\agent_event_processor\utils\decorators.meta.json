{"data_mtime": 1757704599, "dep_lines": [8, 7, 9, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30], "dependencies": ["collections.abc", "functools", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "49a62606276973c4ed65fffc4254102948ceffa9", "id": "src.agent_event_processor.utils.decorators", "ignore_all": false, "interface_hash": "2ff297cac0f3a26301e172d72706728c5b2611cf", "mtime": 1757363360, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "lambdas\\agent-event-processor\\src\\agent_event_processor\\utils\\decorators.py", "plugin_data": null, "size": 758, "suppressed": [], "version_id": "1.17.1"}