#!/usr/bin/env python3
"""Performance testing script for stored procedure optimization.

This script tests both the original and optimized stored procedures
with concurrent operations to measure performance improvements.

Usage:
    $env:AWS_PROFILE="admin-memo"; python test_stored_procedure_performance.py
    $env:AWS_PROFILE="admin-memo"; python test_stored_procedure_performance.py --concurrent 10
    $env:AWS_PROFILE="admin-memo"; python test_stored_procedure_performance.py --iterations 50
"""

import argparse
import asyncio
import hashlib
import json
import random
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timezone
from typing import Dict, List, Tuple

import boto3
from botocore.exceptions import ClientError


class PerformanceTestRunner:
    """Performance test runner for stored procedures."""
    
    def __init__(self, cluster_id: str, database: str, user: str, schema: str = "test_schema"):
        self.cluster_id = cluster_id
        self.database = database
        self.user = user
        self.schema = schema
        self.client = boto3.client('redshift-data', region_name='us-east-1')
    
    def generate_test_data(self, iteration: int, concurrent_id: int = 0) -> Dict:
        """Generate test data for stored procedure call."""
        timestamp = datetime.now(timezone.utc)
        
        # Create some variation but also some overlap for concurrent testing
        tenant_id = f"TestTenant_{iteration % 3}"  # 3 different tenants
        agent_id = f"TestAgent_{(iteration + concurrent_id) % 5}"  # 5 different agents
        
        # Generate unique event hash
        event_data = f"{tenant_id}_{agent_id}_{timestamp.isoformat()}_{iteration}_{concurrent_id}"
        event_hash = hashlib.sha256(event_data.encode()).hexdigest()
        
        return {
            'p_tenant_name': tenant_id,
            'p_timezone_name': 'America/Chicago',
            'p_agent_name': agent_id,
            'p_agent_role': 'Test Agent',
            'p_event_type': random.choice(['Login', 'Logout', 'AgentAvailable', 'AgentBusiedOut']),
            'p_hash_event': event_hash,
            'p_timestamp_utc': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'p_timestamp_local': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'p_operator_id': f'OP_{agent_id}',
            'p_workstation': f'WS_{iteration}',
            'p_media_label': f'MEDIA_{iteration}',
            'p_uri': f'tel:+155500{iteration:04d}',
            'p_device_name': f'Device_{iteration}',
            'p_busied_out_action': 'Manual' if iteration % 2 == 0 else None,
            'p_busied_out_duration': 300 if iteration % 3 == 0 else None,
            'p_reason_code': 'Normal',
            'p_ring_group_name': f'RingGroup_{iteration % 2}' if iteration % 4 == 0 else None,
            'p_ring_group_uri': f'sip:rg{iteration % 2}@test.com' if iteration % 4 == 0 else None,
            'p_json': json.dumps({'test_data': f'iteration_{iteration}', 'concurrent_id': concurrent_id})
        }
    
    def call_stored_procedure(self, procedure_name: str, params: Dict, timeout: int = 30) -> Tuple[bool, float, str]:
        """Call stored procedure and measure execution time."""
        start_time = time.time()
        
        # Build parameter list in correct order
        param_values = [
            params['p_tenant_name'],
            params['p_timezone_name'],
            params['p_agent_name'],
            params['p_agent_role'],
            params['p_event_type'],
            params['p_hash_event'],
            f"'{params['p_timestamp_utc']}'::timestamp",
            f"'{params['p_timestamp_local']}'::timestamp",
            params['p_operator_id'],
            params['p_workstation'],
            params['p_media_label'],
            params['p_uri'],
            params['p_device_name'],
            params['p_busied_out_action'],
            str(params['p_busied_out_duration']) if params['p_busied_out_duration'] else 'NULL',
            params['p_reason_code'],
            params['p_ring_group_name'],
            params['p_ring_group_uri'],
            f"'{params['p_json']}'"
        ]
        
        # Format parameters for SQL call
        formatted_params = []
        for val in param_values:
            if val is None or val == 'NULL':
                formatted_params.append('NULL')
            elif isinstance(val, str) and not val.startswith("'"):
                formatted_params.append(f"'{val}'")
            else:
                formatted_params.append(str(val))
        
        sql = f"CALL {self.schema}.{procedure_name}({', '.join(formatted_params)});"
        
        try:
            response = self.client.execute_statement(
                ClusterIdentifier=self.cluster_id,
                Database=self.database,
                DbUser=self.user,
                Sql=sql,
                WithEvent=True
            )
            
            statement_id = response['Id']
            
            # Wait for completion with timeout
            waiter = self.client.get_waiter('statement_complete')
            waiter.wait(
                Id=statement_id, 
                WaiterConfig={'Delay': 1, 'MaxAttempts': timeout}
            )
            
            # Check result
            result = self.client.describe_statement(Id=statement_id)
            execution_time = time.time() - start_time
            
            if result['Status'] == 'FINISHED':
                return True, execution_time, "Success"
            else:
                error_msg = result.get('Error', f"Status: {result['Status']}")
                return False, execution_time, error_msg
                
        except Exception as e:
            execution_time = time.time() - start_time
            return False, execution_time, str(e)
    
    def run_concurrent_test(self, procedure_name: str, num_concurrent: int, iterations_per_thread: int) -> Dict:
        """Run concurrent test with multiple threads."""
        print(f"\nRunning concurrent test: {procedure_name}")
        print(f"Concurrent threads: {num_concurrent}")
        print(f"Iterations per thread: {iterations_per_thread}")
        print(f"Total operations: {num_concurrent * iterations_per_thread}")
        
        results = {
            'procedure_name': procedure_name,
            'num_concurrent': num_concurrent,
            'iterations_per_thread': iterations_per_thread,
            'total_operations': num_concurrent * iterations_per_thread,
            'successful': 0,
            'failed': 0,
            'execution_times': [],
            'errors': [],
            'start_time': time.time()
        }
        
        def worker_thread(thread_id: int) -> List[Tuple[bool, float, str]]:
            """Worker thread function."""
            thread_results = []
            for i in range(iterations_per_thread):
                test_data = self.generate_test_data(i, thread_id)
                success, exec_time, error_msg = self.call_stored_procedure(procedure_name, test_data)
                thread_results.append((success, exec_time, error_msg))
                
                # Small delay to avoid overwhelming the database
                time.sleep(0.1)
            
            return thread_results
        
        # Execute concurrent threads
        with ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(num_concurrent)]
            
            for future in as_completed(futures):
                try:
                    thread_results = future.result()
                    for success, exec_time, error_msg in thread_results:
                        if success:
                            results['successful'] += 1
                        else:
                            results['failed'] += 1
                            results['errors'].append(error_msg)
                        results['execution_times'].append(exec_time)
                except Exception as e:
                    print(f"Thread execution error: {e}")
                    results['failed'] += 1
                    results['errors'].append(str(e))
        
        results['end_time'] = time.time()
        results['total_time'] = results['end_time'] - results['start_time']
        
        return results
    
    def print_results(self, results: Dict):
        """Print formatted test results."""
        print(f"\n{'='*60}")
        print(f"Results for {results['procedure_name']}")
        print(f"{'='*60}")
        print(f"Total operations: {results['total_operations']}")
        print(f"Successful: {results['successful']}")
        print(f"Failed: {results['failed']}")
        print(f"Success rate: {results['successful']/results['total_operations']*100:.1f}%")
        print(f"Total time: {results['total_time']:.2f} seconds")
        
        if results['execution_times']:
            exec_times = results['execution_times']
            print(f"Average execution time: {sum(exec_times)/len(exec_times):.3f} seconds")
            print(f"Min execution time: {min(exec_times):.3f} seconds")
            print(f"Max execution time: {max(exec_times):.3f} seconds")
            print(f"Operations per second: {results['total_operations']/results['total_time']:.2f}")
        
        if results['errors']:
            print(f"\nFirst 5 errors:")
            for i, error in enumerate(results['errors'][:5]):
                print(f"  {i+1}. {error}")


def main():
    """Main function to run performance tests."""
    parser = argparse.ArgumentParser(description='Test stored procedure performance')
    parser.add_argument('--concurrent', type=int, default=5, help='Number of concurrent threads')
    parser.add_argument('--iterations', type=int, default=10, help='Iterations per thread')
    parser.add_argument('--schema', default='test_schema', help='Database schema to test')
    
    args = parser.parse_args()
    
    print("Stored Procedure Performance Testing")
    print("=" * 70)
    
    # Configuration
    cluster_id = "dev-us-smartanalytics-common-redshift"
    database = "dev"
    user = "solacom"
    
    print(f"Cluster: {cluster_id}")
    print(f"Database: {database}")
    print(f"Schema: {args.schema}")
    print(f"Concurrent threads: {args.concurrent}")
    print(f"Iterations per thread: {args.iterations}")
    
    # Initialize test runner
    runner = PerformanceTestRunner(cluster_id, database, user, args.schema)
    
    # Test original stored procedure
    print(f"\n{'='*70}")
    print("Testing ORIGINAL stored procedure")
    print(f"{'='*70}")
    
    original_results = runner.run_concurrent_test(
        'sp_ingest_agent_event', 
        args.concurrent, 
        args.iterations
    )
    runner.print_results(original_results)
    
    # Test optimized stored procedure
    print(f"\n{'='*70}")
    print("Testing OPTIMIZED stored procedure")
    print(f"{'='*70}")
    
    optimized_results = runner.run_concurrent_test(
        'sp_ingest_agent_event_optimized', 
        args.concurrent, 
        args.iterations
    )
    runner.print_results(optimized_results)
    
    # Comparison
    print(f"\n{'='*70}")
    print("PERFORMANCE COMPARISON")
    print(f"{'='*70}")
    
    if original_results['execution_times'] and optimized_results['execution_times']:
        orig_avg = sum(original_results['execution_times']) / len(original_results['execution_times'])
        opt_avg = sum(optimized_results['execution_times']) / len(optimized_results['execution_times'])
        
        improvement = ((orig_avg - opt_avg) / orig_avg) * 100
        
        print(f"Original average time: {orig_avg:.3f} seconds")
        print(f"Optimized average time: {opt_avg:.3f} seconds")
        print(f"Performance improvement: {improvement:.1f}%")
        
        orig_ops_per_sec = original_results['total_operations'] / original_results['total_time']
        opt_ops_per_sec = optimized_results['total_operations'] / optimized_results['total_time']
        
        print(f"Original throughput: {orig_ops_per_sec:.2f} ops/sec")
        print(f"Optimized throughput: {opt_ops_per_sec:.2f} ops/sec")
        print(f"Throughput improvement: {((opt_ops_per_sec - orig_ops_per_sec) / orig_ops_per_sec) * 100:.1f}%")
    
    print(f"\nOriginal success rate: {original_results['successful']/original_results['total_operations']*100:.1f}%")
    print(f"Optimized success rate: {optimized_results['successful']/optimized_results['total_operations']*100:.1f}%")


if __name__ == "__main__":
    main()
